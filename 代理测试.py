#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🧪 代理连接测试")
print("=" * 40)

try:
    import requests
    print("✅ requests 模块导入成功")
    
    # 强制使用代理
    proxies = {
        'http': 'http://127.0.0.1:7897',
        'https': 'http://127.0.0.1:7897'
    }
    
    print(f"🌐 使用代理: {proxies['http']}")
    
    # 测试Steam API
    url = "https://store.steampowered.com/search/results/?query&start=0&count=100&infinite=1"
    print(f"🔗 请求URL: {url}")
    
    print("⏳ 发送请求中...")
    response = requests.get(url, timeout=15, proxies=proxies)
    
    print(f"📡 响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            total_count = data.get('total_count', 0)
            print(f"✅ 成功！商品总数: {total_count:,}")
            print("🎉 代理连接完全正常！")
        except Exception as e:
            print(f"❌ JSON解析失败: {e}")
    else:
        print(f"❌ HTTP状态码异常: {response.status_code}")
        print(f"响应内容: {response.text[:200]}")
        
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("💡 请运行: pip install requests")
    
except requests.exceptions.ProxyError as e:
    print(f"❌ 代理连接失败: {e}")
    print("💡 请检查代理服务器是否正在运行")
    
except requests.exceptions.Timeout as e:
    print(f"❌ 请求超时: {e}")
    print("💡 请检查网络连接或增加超时时间")
    
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 测试完成")
